import 'package:storetrack_app/shared/models/gallery_info.dart';
import 'package:storetrack_app/shared/models/gallery_section.dart';
import 'package:storetrack_app/shared/models/gallery_photo.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class GalleryService {
  /// Creates gallery sections from photoTagTwo array
  ///
  /// This method ports the Java createSectionViewArrayTwo functionality to Dart.
  /// It processes photoTagsTwo from a question and creates gallery sections with
  /// both dummy "add photo" items and existing photos from the photo folders.
  ///
  /// Parameters:
  /// - [task]: TaskDetail containing forms and photo folders
  /// - [formId]: Form ID to filter by (as String)
  /// - [questionId]: Question ID to filter by (as String)
  ///
  /// Returns GalleryInfo with sections and representative photo
  ///
  /// Example usage:
  /// ```dart
  /// final galleryInfo = GalleryService.createSectionViewArrayTwo(
  ///   task: taskDetail,
  ///   formId: "123",
  ///   questionId: "456",
  /// );
  /// ```
  static GalleryInfo createSectionViewArrayTwo({
    required TaskDetail task,
    required String formId,
    required String questionId,
  }) {
    final galleryInfo = GalleryInfo();
    final List<GallerySection> sectionViewArray = [];

    // Find the form by formId
    Form? form;
    try {
      form = task.forms?.firstWhere(
        (f) => f.formId.toString() == formId,
      );
    } catch (e) {
      // Form not found, return empty gallery info
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    if (form == null) {
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    // Find the question by questionId
    Question? question;
    try {
      question = form.questions?.firstWhere(
        (q) => q.questionId.toString() == questionId,
      );
    } catch (e) {
      // Question not found, return empty gallery info
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    if (question == null || question.photoTagsTwo == null) {
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    // Loop through photoTagsTwo array
    for (final photoTag in question.photoTagsTwo!) {
      // Create section
      final objSection = GallerySection(
        photoTag: photoTag.photoTag,
        isMandatory: photoTag.isMandatory ?? false,
        projectPhotoResolution: photoTag.photoResPerc?.toInt(),
        liveImagesOnly: photoTag.liveImagesOnly ?? false,
        numberOfPhotos: photoTag.numberOfPhotos?.toInt() ?? 0,
        photoTagID: photoTag.photoTagId?.toString(),
        measurementPhotoTypeID: photoTag.measurementPhototypeId?.toString(),
        combineTypeID: "2",
        items: [],
      );

      final List<GalleryPhoto> items = [];

      // Add dummy photo (Add Image Button)
      final dummyPhoto = GalleryPhoto(
        isAddImageButton: true,
        taskID: task.taskId.toString(),
        photoTagID: photoTag.photoTagId?.toString(),
        formID: formId,
        questionID: questionId,
        measurementID: "0",
        measurementPhotoTypeID: photoTag.measurementPhototypeId?.toString(),
        questionPartID: "0",
        questionMultiPartID: "0",
        combineTypeID: "2",
        photoCaption:
            "DummyImageAddPhoto", // TODO: Replace with localized string
        photoTag: photoTag.photoTag,
        photoID: null,
        isPhotoTagMandatory: photoTag.isMandatory ?? false,
        photoCompression: photoTag.photoResPerc?.toInt() ?? 0,
        isLiveImagesOnly: photoTag.liveImagesOnly ?? false,
      );

      items.add(dummyPhoto);

      // Add existing undeleted photos
      // Find photo folder by photoTagId
      PhotoFolder? photoFolder;
      try {
        photoFolder = task.photoFolder?.firstWhere(
          (folder) =>
              folder.folderId?.toString() == photoTag.photoTagId?.toString(),
        );
      } catch (e) {
        photoFolder = null;
      }

      if (photoFolder != null && photoFolder.photos != null) {
        for (final photo in photoFolder.photos!) {
          if (photo.userDeletedPhoto != true) {
            final photoFormId = photo.formId?.toString();
            final photoQuestionId = photo.questionId?.toString();
            final photoCombineId = photo.combineTypeId?.toString();

            if (photoCombineId == "2" &&
                photoFormId == formId &&
                photoQuestionId == questionId) {
              // This is the matching photo
              final item = GalleryPhoto(
                photoCaption: photo.caption,
                taskID: task.taskId.toString(),
                photoTagID: photoTag.photoTagId?.toString(),
                formID: photo.formId?.toString(),
                questionID: photo.questionId?.toString(),
                measurementID: photo.measurementId?.toString(),
                questionPartID: photo.questionpartId?.toString(),
                questionMultiPartID: photo.questionPartMultiId,
                measurementPhotoTypeID:
                    photoTag.measurementPhototypeId?.toString(),
                combineTypeID: "2",
                photoTag: photoTag.photoTag,
                photoID: photo.photoId?.toString(),
                photoURL: photo.photoUrl,
                photoLocalPath: null, // Photo entity doesn't have local path
                isSynced: true, // Assume synced if it exists in API response
                isCannotUploadMandatory: photo.cannotUploadMandatory ?? false,
                isPhotoTagMandatory: photoTag.isMandatory ?? false,
                photoCompression: photoTag.photoResPerc?.toInt() ?? 0,
                isLiveImagesOnly: photoTag.liveImagesOnly ?? false,
              );

              items.add(item);
            }
          }
        }
      }

      objSection.items = items;
      sectionViewArray.add(objSection);
    }

    // Debug output - can be removed in production
    // print("sectionViewArray: $sectionViewArray");

    galleryInfo.sectionViewArray = sectionViewArray;

    // Find representative photo
    final representativePhoto = findPhotoRepresentative(sectionViewArray);
    galleryInfo.representativePhoto = representativePhoto;

    return galleryInfo;
  }

  /// Creates gallery sections from photoTagsThree array
  ///
  /// This method ports the Java createSectionViewArrayThree functionality to Dart.
  /// It processes photoTagsThree from a question with additional filtering by
  /// questionPartId, measurementId, and questionMultiPartId.
  ///
  /// Parameters:
  /// - [task]: TaskDetail containing forms and photo folders
  /// - [formId]: Form ID to filter by (as String)
  /// - [questionId]: Question ID to filter by (as String)
  /// - [questionPartId]: Question Part ID to filter by (as String)
  /// - [measurementId]: Measurement ID to filter by (as String)
  /// - [questionMultiPartId]: Question Multi Part ID to filter by (as String)
  ///
  /// Returns GalleryInfo with sections and representative photo
  ///
  /// Example usage:
  /// ```dart
  /// final galleryInfo = GalleryService.createSectionViewArrayThree(
  ///   task: taskDetail,
  ///   formId: "123",
  ///   questionId: "456",
  ///   questionPartId: "789",
  ///   measurementId: "101",
  ///   questionMultiPartId: "112",
  /// );
  /// ```
  static GalleryInfo createSectionViewArrayThree({
    required TaskDetail task,
    required String formId,
    required String questionId,
    required String questionPartId,
    required String measurementId,
    required String questionMultiPartId,
  }) {
    final galleryInfo = GalleryInfo();
    final List<GallerySection> sectionViewArray = [];

    // Error check - return empty if required parameters are null/empty
    if (questionPartId.isEmpty || measurementId.isEmpty) {
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    // Find the form by formId
    Form? form;
    try {
      form = task.forms?.firstWhere(
        (f) => f.formId.toString() == formId,
      );
    } catch (e) {
      // Form not found, return empty gallery info
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    if (form == null) {
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    // Find the question by questionId
    Question? question;
    try {
      question = form.questions?.firstWhere(
        (q) => q.questionId.toString() == questionId,
      );
    } catch (e) {
      // Question not found, return empty gallery info
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    if (question == null || question.photoTagsThree == null) {
      galleryInfo.sectionViewArray = sectionViewArray;
      return galleryInfo;
    }

    // Loop through photoTagsThree array
    for (final photoTag in question.photoTagsThree!) {
      // Check if this photo tag matches the required questionPartId and measurementId
      if (photoTag.questionpartId?.toString() == questionPartId &&
          photoTag.measurementId?.toString() == measurementId) {
        // This photo tag is the matching one

        // Create section
        final objSection = GallerySection(
          photoTag: photoTag.photoTag,
          isMandatory: photoTag.isMandatory ?? false,
          projectPhotoResolution: photoTag.photoResPerc?.toInt(),
          liveImagesOnly: photoTag.liveImagesOnly ?? false,
          numberOfPhotos: photoTag.numberOfPhotos?.toInt() ?? 0,
          photoTagID: photoTag.photoTagId?.toString(),
          measurementPhotoTypeID: photoTag.measurementPhototypeId?.toString(),
          combineTypeID: "3",
          items: [],
        );

        final List<GalleryPhoto> items = [];

        // Add dummy photo (Add Image Button)
        final dummyPhoto = GalleryPhoto(
          isAddImageButton: true,
          taskID: task.taskId.toString(),
          photoTagID: photoTag.photoTagId?.toString(),
          formID: formId,
          questionID: questionId,
          measurementID: measurementId,
          measurementPhotoTypeID: photoTag.measurementPhototypeId?.toString(),
          questionPartID: questionPartId,
          questionMultiPartID: questionMultiPartId,
          combineTypeID: "3",
          photoCaption:
              "DummyImageAddPhoto", // TODO: Replace with localized string
          photoTag: photoTag.photoTag,
          photoID: null,
          isPhotoTagMandatory: photoTag.isMandatory ?? false,
          photoCompression: photoTag.photoResPerc?.toInt() ?? 0,
          isLiveImagesOnly: photoTag.liveImagesOnly ?? false,
        );

        items.add(dummyPhoto);

        // Add existing undeleted photos
        // Find photo folder by photoTagId
        PhotoFolder? photoFolder;
        try {
          photoFolder = task.photoFolder?.firstWhere(
            (folder) =>
                folder.folderId?.toString() == photoTag.photoTagId?.toString(),
          );
        } catch (e) {
          photoFolder = null;
        }

        if (photoFolder != null && photoFolder.photos != null) {
          for (final photo in photoFolder.photos!) {
            if (photo.userDeletedPhoto != true) {
              final photoFormId = photo.formId?.toString();
              final photoQuestionId = photo.questionId?.toString();
              final photoQuestionPartId = photo.questionpartId?.toString();
              final photoMeasurementId = photo.measurementId?.toString();
              final photoCombineId = photo.combineTypeId?.toString();
              final photoQuestionMultiPartId = photo.questionPartMultiId;

              if (photoCombineId == "3" &&
                  photoFormId == formId &&
                  photoQuestionId == questionId &&
                  photoQuestionPartId == questionPartId &&
                  photoMeasurementId == measurementId &&
                  photoQuestionMultiPartId == questionMultiPartId) {
                // This is the matching photo
                final item = GalleryPhoto(
                  photoCaption: photo.caption,
                  taskID: task.taskId.toString(),
                  photoTagID: photoTag.photoTagId?.toString(),
                  formID: photo.formId?.toString(),
                  questionID: photo.questionId?.toString(),
                  measurementID: photo.measurementId?.toString(),
                  questionPartID: photo.questionpartId?.toString(),
                  questionMultiPartID: photo.questionPartMultiId,
                  measurementPhotoTypeID:
                      photoTag.measurementPhototypeId?.toString(),
                  combineTypeID: "3",
                  photoTag: photoTag.photoTag,
                  photoID: photo.photoId?.toString(),
                  photoURL: photo.photoUrl,
                  photoLocalPath: null, // Photo entity doesn't have local path
                  imageRec: photoFolder.imageRec ?? false,
                  isSynced: true, // Assume synced if it exists in API response
                  isCannotUploadMandatory: photo.cannotUploadMandatory ?? false,
                  isPhotoTagMandatory: photoTag.isMandatory ?? false,
                  photoCompression: photoTag.photoResPerc?.toInt() ?? 0,
                  isLiveImagesOnly: photoTag.liveImagesOnly ?? false,
                );

                items.add(item);
              }
            }
          }
        }

        objSection.items = items;
        sectionViewArray.add(objSection);
      }
    }

    // Debug output - can be removed in production
    // print("sectionViewArray: $sectionViewArray");

    galleryInfo.sectionViewArray = sectionViewArray;

    // Find representative photo
    final representativePhoto = findPhotoRepresentative(sectionViewArray);
    galleryInfo.representativePhoto = representativePhoto;

    return galleryInfo;
  }

  /// Finds a representative photo from the gallery sections
  /// Returns the first non-dummy photo found, or null if none exists
  static GalleryPhoto? findPhotoRepresentative(List<GallerySection> sections) {
    for (final section in sections) {
      for (final photo in section.items) {
        // Skip dummy photos (add image buttons)
        if (!photo.isAddImageButton && photo.photoID != null) {
          return photo;
        }
      }
    }
    return null;
  }
}
